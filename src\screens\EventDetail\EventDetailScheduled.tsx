import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Calendar,
  DollarSign,
  Camera,
  MapPin
} from 'lucide-react';
import Layout from '../../components/common/Layout';
import { Event } from '../../types/events';
import { AppDispatch } from '../../store/store';
import { selectAuth } from '../../store/slices/authSlice';
import {
  selectSelectedEvent,
  selectEventsLoading,
  selectEventsError,
} from '../../store/slices/eventsSlice';
import {
  fetchEventDetails,
} from '../../store/thunks/eventsThunks';

interface EventDetailScheduledProps {
  eventId?: string;
}

const EventDetailScheduled: React.FC<EventDetailScheduledProps> = ({ eventId: propEventId }) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { eventId: urlEventId } = useParams<{ eventId: string }>();
  const [eventData, setEventData] = useState<Event | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redux state
  const { tokens } = useSelector(selectAuth);
  const selectedEvent = useSelector(selectSelectedEvent);
  const loading = useSelector(selectEventsLoading);
  const reduxError = useSelector(selectEventsError);

  // Use URL eventId, then prop eventId, then selected event ID
  const currentEventId = urlEventId || propEventId || selectedEvent?.id;



  // Fetch event details on mount
  useEffect(() => {
    const fetchEventData = async () => {
      if (!currentEventId || !tokens?.access) {
        console.log('🔍 EventDetailScheduled - Missing eventId or token:', {
          currentEventId,
          hasToken: !!tokens?.access
        });
        setIsLoading(false);
        return;
      }

      console.log('🔍 EventDetailScheduled - Fetching event data:', {
        eventId: currentEventId,
        urlEventId,
        propEventId,
        hasToken: !!tokens?.access
      });

      try {
        setIsLoading(true);
        setError(null);

        // Fetch event details using Redux thunk
        const eventResult = await dispatch(fetchEventDetails({
          eventId: currentEventId,
          token: tokens.access
        })).unwrap();

        console.log('🔍 EventDetailScheduled - Event data received:', eventResult);

        // Check if this event should be on this screen
        if (eventResult.event_status !== 'scheduled') {
          console.log('⚠️ Event status mismatch - redirecting:', {
            eventStatus: eventResult.event_status,
            expectedStatus: 'scheduled'
          });

          // Redirect to appropriate screen based on event status
          if (eventResult.event_status === 'ongoing' || eventResult.event_status === 'ongoing_with_buffer') {
            navigate(`/event-detail-in-progress/${currentEventId}`, { replace: true });
          } else if (eventResult.event_status === 'completed') {
            navigate(`/event-detail-completed/${currentEventId}`, { replace: true });
          }
          return;
        }

        setEventData(eventResult);

      } catch (err: any) {
        console.error('💥 EventDetailScheduled - Error fetching event:', err);
        setError(err.message || 'Failed to load event details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchEventData();
  }, [dispatch, currentEventId, tokens?.access, urlEventId, propEventId, navigate]);

  // Use API event data, fallback to selected event from Redux
  const displayEvent = eventData || selectedEvent;

  // Calculate expected revenue for scheduled events
  const getExpectedRevenue = () => {
    const pricePerPhoto = parseFloat(displayEvent.image_price_limit || '0');

    return {
      revenue: `$${pricePerPhoto.toFixed(2)}`,
      revenueMode: 'Pay Per Photo'
    };
  };

  const revenueInfo = getExpectedRevenue();

  return (
    <Layout
      searchPlaceholder="Search here"
      showRightSidebar={false}
    >
      {/* Left Side - Event Details */}
      <div className="flex-1 p-6 overflow-y-auto">
        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-12">
            <div className="text-gray-500">Loading event details...</div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex justify-center items-center py-12">
            <div className="text-red-500">Error loading event: {error}</div>
          </div>
        )}

        {/* Event Header Card */}
        {displayEvent && !loading.eventDetails && (
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <div className="flex items-center gap-4 mb-4">
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 font-bold text-2xl">
                  {displayEvent.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl font-bold text-gray-900 text-left">{displayEvent.name}</h1>
                  <span className="px-3 py-1 text-sm font-medium text-orange-600 bg-orange-100 rounded-full">
                    Scheduled
                  </span>
                </div>
                <p className="text-gray-600 text-left">{displayEvent.description || 'No description available'}</p>
              </div>
              <div className="flex items-center">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                  Join Event
                </button>
              </div>
            </div>

            {/* Overview Tab */}
            <div className="flex gap-8 border-b border-gray-200">
              <button className="text-sm font-medium text-blue-600 border-b-2 border-blue-600 pb-2">
                Overview
              </button>
            </div>
          </div>
        )}

        {/* Event Details Cards - 2x2 Grid */}
        {selectedEvent && (
          <div className="grid grid-cols-2 gap-6 mb-6">
            {/* Date Card */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Calendar size={20} className="text-blue-600" />
                </div>
              </div>
              <div className="text-left">
                <p className="text-sm text-gray-500 mb-1">Start Date</p>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  {new Date(displayEvent.start_date).toLocaleDateString()}
                </p>
                <p className="text-xs text-gray-500">
                  Event scheduled to start soon
                </p>
              </div>
            </div>

            {/* Revenue Mode Card */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign size={20} className="text-green-600" />
                </div>
              </div>
              <div className="text-left">
                <p className="text-sm text-gray-500 mb-1">Price Per Photo</p>
                <p className="text-2xl font-bold text-gray-900 mb-1">{revenueInfo.revenue}</p>
                <p className="text-xs text-gray-500">{revenueInfo.revenueMode}</p>
              </div>
            </div>

            {/* Expected Photos Card */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Camera size={20} className="text-purple-600" />
                </div>
              </div>
              <div className="text-left">
                <p className="text-sm text-gray-500 mb-1">Photos Uploaded</p>
                <p className="text-2xl font-bold text-gray-900">{displayEvent.total_photos}</p>
                <p className="text-xs text-gray-500">Event not started yet</p>
              </div>
            </div>

            {/* Location Card */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <MapPin size={20} className="text-orange-600" />
                </div>
              </div>
              <div className="text-left">
                <p className="text-sm text-gray-500 mb-1">Location</p>
                <p className="text-lg font-bold text-gray-900">{displayEvent.location}</p>
              </div>
            </div>
          </div>
        )}

        {/* Empty State for Scheduled Event */}
        <div className="bg-white rounded-lg border-2 border-dashed border-gray-300 p-16 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <Camera size={24} className="text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Get Ready For The Event To Start</h3>
          <p className="text-gray-500 text-sm max-w-md mx-auto">
            This event is scheduled to begin soon. Once it starts, you'll be able to upload and manage photos here.
          </p>
        </div>
      </div>
    </Layout>
  );
};

export default EventDetailScheduled;
